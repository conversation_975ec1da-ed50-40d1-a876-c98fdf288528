package adhoc.mock

import org.web3j.abi.DefaultFunctionReturnDecoder
import org.web3j.abi.EventEncoder
import org.web3j.abi.TypeReference
import org.web3j.abi.datatypes.Address
import org.web3j.abi.datatypes.Array
import org.web3j.abi.datatypes.Bool
import org.web3j.abi.datatypes.DynamicArray
import org.web3j.abi.datatypes.DynamicStruct
import org.web3j.abi.datatypes.Event
import org.web3j.abi.datatypes.StaticStruct
import org.web3j.abi.datatypes.Utf8String
import org.web3j.abi.datatypes.generated.Bytes32
import org.web3j.abi.datatypes.generated.Uint16
import org.web3j.abi.datatypes.generated.Uint256
import org.web3j.protocol.core.methods.response.Log

/**
 * Factory class for creating mock event objects used in testing.
 * This class centralizes the creation of various mock objects related to blockchain events,
 * improving maintainability and reusability across test classes.
 *
 * Note: This class provides data and configuration for mocks, but the actual Mock() creation
 * must be done within Spock test classes.
 */
class EventMockFactory {
	// converts to "TEST_PROVIDER_ID"
	public static final String PROVIDER_ID_HEX = "0x544553545f50524f56494445525f494400000000000000000000000000000000"

	// providerEoa: ****************************************** (20 bytes)
	public static final String PROVIDER_EOA_HEX_20_BYTES = "******************************************"

	// traceId: converts to "TRACE_ADD_PROVIDER" (32 bytes)
	public static final String TRACE_ADD_PROVIDER = "54524143455f4144445f50524f56494445520000000000000000000000000000"

	// tokenId: converts to "TEST_TOKEN_ID"
	public static final String TOKEN_ID_HEX = "0x544553545f544f4b454e5f494400000000000000000000000000000000000000"

	// traceId: converts to "TRACE_ADD_TOKEN"
	public static final String TRACE_ADD_TOKEN_HEX = "54524143455f4144445f544f4b454e0000000000000000000000000000000000"

	// traceId: converts to "TRACE_TRANSFER" with 0xff at the end
	public static final String TRACE_TRANSFER_HEX = [84,82,65,67,69,95,84,82,65,78,83,70,69,82,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,-1]

	// accountId: converts to "TEST_ACCOUNT_ID"
	public static final String ACCOUNT_ID_HEX = "0x544553545f4143434f554e545f494400000000000000000000000000000000"

	/**
	 * Create a mock AddProviderRole event definition for testing
	 * Event: AddProviderRole(bytes32 indexed providerId, address providerEoa, bytes32 traceId)
	 * @return Event definition for AddProviderRole
	 */
	static Event createMockAddProviderRoleEvent() {
		def parameters = [
			// providerId (indexed)
			new TypeReference<Bytes32>(true) {},
			// providerEoa (non-indexed)
			new TypeReference<Address>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("AddProviderRole", parameters)
	}

	/**
	 * Create a mock AddTokenByProvider event definition for testing
	 * Event: AddTokenByProvider(bytes32 indexed providerId, bytes32 tokenId, bytes32 traceId)
	 * @return Event definition for AddTokenByProvider
	 */
	static Event createMockAddTokenByProviderEvent() {
		def parameters = [
			// providerId (indexed)
			new TypeReference<Bytes32>(true) {},
			// tokenId (non-indexed)
			new TypeReference<Bytes32>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("AddTokenByProvider", parameters)
	}

	/**
	 * Create a mock AddRoleAdminChanged event definition for testing
	 * Event: AddRoleAdminChanged(bytes32 indexed role, bytes32 indexed previousAdminRole, bytes32 indexed newAdminRole)
	 * @return Event definition for AddRoleAdminChanged
	 */
	static Event createMockAddRoleAdminChangedEvent() {
		def parameters = [
			// role (indexed)
			new TypeReference<Bytes32>(true) {},
			// previousAdminRole (indexed)
			new TypeReference<Bytes32>(true) {},
			// newAdminRole (indexed)
			new TypeReference<Bytes32>(true) {}
		]
		return new Event("RoleAdminChanged", parameters)
	}

	/**
	 * Create a mock RoleGranted event definition for testing
	 * Event: RoleGranted(bytes32 indexed role, address indexed account, address indexed sender)
	 * @return Event definition for RoleGranted
	 */
	static Event createMockRoleGrantedEvent() {
		def parameters = [
			// role (indexed)
			new TypeReference<Bytes32>(true) {},
			// account (indexed)
			new TypeReference<Address>(true) {},
			// sender (indexed)
			new TypeReference<Address>(true) {}
		]
		return new Event("RoleGranted", parameters)
	}

	/**
	 * Create a mock RoleRevoked event definition for testing
	 * Event: RoleRevoked(bytes32 indexed role, address indexed account, address indexed sender)
	 * @return Event definition for RoleRevoked
	 */
	static Event createMockRoleRevokedEvent() {
		def parameters = [
			// role (indexed)
			new TypeReference<Bytes32>(true) {},
			// account (indexed)
			new TypeReference<Address>(true) {},
			// sender (indexed)
			new TypeReference<Address>(true) {}
		]
		return new Event("RoleRevoked", parameters)
	}

	/**
	 * Create a mock ModProvider event definition for testing
	 * Event: ModProvider(bytes32 indexed providerId, bytes32 name, bytes32 traceId)
	 * @return Event definition for ModProvider
	 */
	static Event createMockModProviderEvent() {
		def parameters = [
			// providerId (indexed)
			new TypeReference<Bytes32>(true) {},
			// name (non-indexed)
			new TypeReference<Bytes32>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("ModProvider", parameters)
	}

	/**
	 * Create a mock ModAccount event definition for testing
	 * Event: ModAccount(bytes32 accountId, string accountName, bytes32 traceId)
	 * @return Event definition for ModAccount
	 */
	static Event createMockModAccountEvent() {
		def parameters = [
			// accountId (non-indexed)
			new TypeReference<Bytes32>(false) {},
			// accountName (non-indexed)
			new TypeReference<Utf8String>(false) {},
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("ModAccount", parameters)
	}

	/**
	 * Create a mock AddAccountLimit event definition for testing (Version 3000)
	 * Event: AddAccountLimit(bytes32 accountId, tuple limitValues, bytes32 traceId)
	 * @return Event definition for AddAccountLimit (Version 3000)
	 */
	static Event createMockAddAccountLimitEvent() {
		TypeReference<LimitValuesStruct> limitValuesTypeReference = new TypeReference<LimitValuesStruct>(false) {}

		def parameters = [
			// accountId (non-indexed)
			new TypeReference<Bytes32>(false) {},
			// limitValues (tuple, non-indexed)
			limitValuesTypeReference,
			// traceId (non-indexed)
			new TypeReference<Bytes32>(false) {}
		]
		return new Event("AddAccountLimit", parameters)
	}

	/**
	 * Struct for AddAccountLimit event (Version 3000) - LimitValues tuple
	 */
	static class LimitValuesStruct extends DynamicStruct {
		LimitValuesStruct(
				Uint256 mint,
				Uint256 burn,
				Uint256 charge,
				Uint256 discharge,
				Uint256 transfer,
				CumulativeLimitsStruct cumulative
		) {
			super(Arrays.asList(mint, burn, charge, discharge, transfer, cumulative))
		}
	}

	/**
	 * Struct for AddAccountLimit event (Version 3000) - Cumulative limits nested tuple
	 */
	static class CumulativeLimitsStruct extends DynamicStruct {
		CumulativeLimitsStruct(
				Uint256 total,
				Uint256 mint,
				Uint256 burn,
				Uint256 charge,
				Uint256 discharge,
				Uint256 transfer
		) {
			super(Arrays.asList(total, mint, burn, charge, discharge, transfer))
		}
	}

	/**
	 *
	 */
	static class TransferDataStruct extends DynamicStruct {
		TransferDataStruct(
				Bytes32 transferType,
				Uint16 zoneId,
				Bytes32 fromValidatorId,
				Bytes32 toValidatorId,
				Uint256 fromAccountBalance,
				Uint256 toAccountBalance,
				Uint256 businessZoneBalance,
				Uint16 bizZoneId,
				Bytes32 sendAccountId,
				Bytes32 fromAccountId,
				Utf8String fromAccountName,
				Bytes32 toAccountId,
				Utf8String toAccountName,
				Uint256 amount,
				Bytes32 miscValue1,
				Utf8String miscValue2,
				Utf8String memo
		) {
			super(Arrays.asList(transferType, zoneId, fromValidatorId, toValidatorId, fromAccountBalance, toAccountBalance, businessZoneBalance, bizZoneId, sendAccountId, fromAccountId, fromAccountName, toAccountId, toAccountName, amount, miscValue1, miscValue2, memo))
		}
	}

	/**
	 * Create a mock Transfer event definition for testing
	 * Event: Transfer(tuple transferData, bytes32 traceId)
	 * Based on the actual Token.json ABI structure with proper tuple definition
	 * @return Event definition for Transfer
	 */
	static Event createMockTransferEvent() {
		// Create the tuple structure based on Token.json ABI
		// The tuple has 17 components as defined in the ABI
		def tupleComponents = [
				new TypeReference<Bytes32>(false) {}, // transferType
				new TypeReference<Uint16>(false) {}, // zoneId
				new TypeReference<Bytes32>(false) {}, // fromValidatorId
				new TypeReference<Bytes32>(false) {}, // toValidatorId
				new TypeReference<Uint256>(false) {}, // fromAccountBalance
				new TypeReference<Uint256>(false) {}, // toAccountBalance
				new TypeReference<Uint256>(false) {}, // businessZoneBalance
				new TypeReference<Uint16>(false) {}, // bizZoneId
				new TypeReference<Bytes32>(false) {}, // sendAccountId
				new TypeReference<Bytes32>(false) {}, // fromAccountId
				new TypeReference<Utf8String>(false) {}, // fromAccountName
				new TypeReference<Bytes32>(false) {}, // toAccountId
				new TypeReference<Utf8String>(false) {}, // toAccountName
				new TypeReference<Uint256>(false) {}, // amount
				new TypeReference<Bytes32>(false) {}, // miscValue1
				new TypeReference<Utf8String>(false) {}, // miscValue2
				new TypeReference<Utf8String>(false) {} // memo
				]
		// Use custom StaticStruct TypeReference that EventEncoder can handle
		TypeReference<TransferDataStruct> tupleTypeReference = new TypeReference<TransferDataStruct>(false) {
//			{
//				innerTypes = tupleComponents
//			}
		}

		def parameters = [
			tupleTypeReference, // transferData (tuple, non-indexed)
			new TypeReference<Bytes32>(false) {} // traceId (non-indexed)
		]
		return new Event("Transfer", parameters)
	}

	/**
	 * Create a proper Transfer log for testing tuple types
	 * Uses correct ABI encoding for the 17-component tuple structure
	 * @return Log object with Transfer event data containing proper tuple
	 */
	static Log createMockTransferLog2() {
		def log = new Log()

		// Use Token contract address
		log.address = "0x88eEA3e4F0839B74A8dE27951bc630126837d646"

		def transferEvent = createMockTransferEvent()
		def eventSignature = EventEncoder.encode(transferEvent)
		log.topics = [eventSignature]

		// Set other required fields
		log.blockNumber = "0xc8" // 200 in hex
		log.transactionHash = "0xabc123456789"
		log.logIndex = "0x0"
		log.blockHash = "0xblockhash123"
		log.transactionIndex = "0x0"
		log.removed = false

		return log
	}

	/**
	 * Create a proper AddTokenByProvider log for normal testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with AddTokenByProvider event data
	 */
	static Log createAddTokenByProviderLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

		// Calculate AddTokenByProvider event signature using Web3j
		def addTokenByProviderEvent = createMockAddTokenByProviderEvent()
		def eventSignature = EventEncoder.encode(addTokenByProviderEvent)
		println("AddTokenByProvider event signature: ${eventSignature}")

		log.topics = [eventSignature, PROVIDER_ID_HEX]

		// Data contains: bytes32 tokenId + bytes32 traceId
		// tokenId: converts to "TEST_TOKEN_ID"
		// traceId: converts to "TRACE_ADD_TOKEN"
		log.data = TOKEN_ID_HEX + TRACE_ADD_TOKEN_HEX

		return log
	}

	/**
	 * Create a proper AddProviderRole log for normal testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with AddProviderRole event data
	 */
	static Log createAddProviderRoleLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

		// Calculate AddProviderRole event signature using Web3j
		def addProviderRoleEvent = createMockAddProviderRoleEvent()
		def eventSignature = EventEncoder.encode(addProviderRoleEvent)
		println("AddProviderRole event signature: ${eventSignature}")

		// providerId (indexed parameter) - converts to "TEST_PROVIDER_ID"
		def providerId = PROVIDER_ID_HEX

		log.topics = [eventSignature, providerId]

		// Data contains: address providerEoa + bytes32 traceId
		// providerEoa: ****************************************** (20 bytes, padded to 32)
		// traceId: converts to "TRACE_ADD_PROVIDER" (32 bytes)
		log.data = to32Bytes(PROVIDER_EOA_HEX_20_BYTES) + TRACE_ADD_PROVIDER

		return log
	}

	/**
	 * Create a proper RoleAdminChanged log for case empty traceId in non-indexed values
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with RoleAdminChanged event data
	 */
	static Log createRoleAdminChangedLog() {
		def log = new Log()

		// Use AccessControl contract address
		log.address = "0xF27289E45825f7E8F3eAE5c3F52e05c8FB6fD3d4"

		// Calculate RoleAdminChanged event signature using Web3j
		def event = createMockAddRoleAdminChangedEvent()
		def eventSignature = EventEncoder.encode(event)
		println("RoleAdminChanged event signature: ${eventSignature}")

		def role = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"
		def previousAdminRole = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"
		def newAdminRole = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"

		log.topics = [
			eventSignature,
			role,
			previousAdminRole,
			newAdminRole
		]

		return log
	}

	/**
	 * Create a proper RoleGranted log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with RoleGranted event data
	 */
	static Log createMockRoleGrantedLog() {
		def log = new Log()

		// Use AccessControl contract address
		log.address = "0xF27289E45825f7E8F3eAE5c3F52e05c8FB6fD3d4"

		// Calculate RoleGranted event signature using Web3j
		def event = createMockRoleGrantedEvent()
		def eventSignature = EventEncoder.encode(event)
		println("RoleGranted event signature: ${eventSignature}")

		def role = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"
		def account = "0x544553545f4143434f554e545f49440000000000000000000000000000000000" // "TEST_ACCOUNT_ID"
		def sender = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"

		log.topics = [
			eventSignature,
			role,
			account,
			sender
		]

		return log
	}

	/**
	 * Create a proper RoleRevoked log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with RoleRevoked event data
	 */
	static Log createMockRoleRevokedLog() {
		def log = new Log()

		// Use AccessControl contract address
		log.address = "0xF27289E45825f7E8F3eAE5c3F52e05c8FB6fD3d4"

		// Calculate RoleRevoked event signature using Web3j
		def event = createMockRoleRevokedEvent()
		def eventSignature = EventEncoder.encode(event)
		println("RoleRevoked event signature: ${eventSignature}")

		def role = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"
		def account = "0x544553545f4143434f554e545f49440000000000000000000000000000000000" // "TEST_ACCOUNT_ID"
		def sender = PROVIDER_ID_HEX // "TEST_PROVIDER_ID"

		log.topics = [
			eventSignature,
			role,
			account,
			sender
		]

		return log
	}

	/** Create a proper ModProvider log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with ModProvider event data
	 */
	static Log createMockModProviderLog() {
		def log = new Log()

		// Use Provider contract address
		log.address = "0xb0BDD71BdB22B3d0b3B6dD8C47DF0f3C658eA22A"

		// Calculate ModProvider event signature using Web3j
		def modProviderEvent = createMockModProviderEvent()
		def eventSignature = EventEncoder.encode(modProviderEvent)
		println("ModProvider event signature: ${eventSignature}")

		// providerId (indexed parameter) - converts to "TEST_PROVIDER_ID"
		def providerId = PROVIDER_ID_HEX

		log.topics = [eventSignature, providerId]

		// Data contains: bytes32 name + bytes32 traceId
		// name: converts to "PROVIDER_NAME"
		// traceId: converts to "TRACE_MOD_PROVIDER"
		log.data = "0x50524f56494445525f4e414d4500000000000000000000000000000000000000" +
				"54524143455f4d4f445f50524f56494445520000000000000000000000000000"

		return log
	}

	/**
	 * Create a proper ModAccount log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with ModAccount event data
	 */
	static Log createMockModAccountLog() {
		def log = new Log()

		// Use Account contract address
		log.address = "0x993366A606A99129e56B4b99B27e428ba1Cb672f"

		// Calculate ModAccount event signature using Web3j
		def modAccountEvent = createMockModAccountEvent()
		def eventSignature = EventEncoder.encode(modAccountEvent)
		println("ModAccount event signature: ${eventSignature}")

		// ModAccount has no indexed parameters, so topics only contains event signature
		log.topics = [eventSignature]

		// Data contains: bytes32 accountId + string accountName + bytes32 traceId
		// For ABI encoding with parameters (bytes32, string, bytes32):
		// - First 32 bytes: accountId (fixed size, goes first)
		// - Next 32 bytes: offset to string data (0x60 = 96 bytes, pointing after all fixed-size params)
		// - Next 32 bytes: traceId (fixed size)
		// - At offset 0x60: string length (32 bytes) + string data (padded to 32-byte boundary)

		def accountId = "1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcdef"
		def traceId = "ef1234567890abcdef1234567890abcdef1234567890abcdef1234567890abcd"
		def accountName = "TestAccount"
		def accountNameHex = accountName.bytes.encodeHex().toString().padRight(64, '0')
		def accountNameLength = String.format("%064x", accountName.length())

		log.data = "0x" +
				accountId + // accountId (bytes32)
				"0000000000000000000000000000000000000000000000000000000000000060" + // offset to string (96 bytes)
				traceId + // traceId (bytes32)
				accountNameLength + // string length
				accountNameHex // string data

		return log
	}

	/**
	 * Create a proper Transfer log for testing tuple types
	 * Uses correct ABI encoding for the 17-component tuple structure
	 * @return Log object with Transfer event data containing proper tuple
	 */
	static Log createMockTransferLog() {
		def log = new Log()

		// Use Token contract address
		log.address = "0x88eEA3e4F0839B74A8dE27951bc630126837d646"

		def transferEvent = createMockTransferEvent()
		def eventSignature = EventEncoder.encode(transferEvent)
		log.topics = [eventSignature]

		// Build the complete data field for Transfer(tuple transferData, bytes32 traceId)
		// Since both parameters are non-indexed, they go in the data field
		log.data = "0x0000000000000000000000000000000000000000000000000000000000000040747261636549640000000000000000000000000000000000000000000000000063686172676500000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000bb8383838380000000000000000000000000000000000000000000000000000000038383838000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000122a00000000000000000000000000000000000000000000000000000000000000320000000000000000000000000000000000000000000000000000000000000bb9333032000000000000000000000000000000000000000000000000000000000033303200000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000220333030000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002a00000000000000000000000000000000000000000000000000000000000000032000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002c000000000000000000000000000000000000000000000000000000000000002e00000000000000000000000000000000000000000000000000000000000000042307836313633363336663735366537343333303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030303030000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"

		// Set other required fields
		log.blockNumber = "0xc8" // 200 in hex
		log.transactionHash = "0xabc123456789"
		log.logIndex = "0x0"
		log.blockHash = "0xblockhash123"
		log.transactionIndex = "0x0"
		log.removed = false

		return log
	}

	/**
	 * Create a proper AddAccountLimit log for testing (Version 3000)
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with AddAccountLimit event data (Version 3000)
	 */
	static Log createMockAddAccountLimitLog() {
		def log = new Log()
		// Use FinancialZoneAccount contract address (3000)
		log.address = "0xF908c90F27013E2E85eB6aF516F7363C674BBeC3"

		// Calculate AddAccountLimit event signature using Web3j
		def addAccountLimitEvent = createMockAddAccountLimitEvent()
		def eventSignature = EventEncoder.encode(addAccountLimitEvent)

		// AddAccountLimit has no indexed parameters, so topics only contains event signature
		log.topics = [eventSignature]
		log.data = "0x3330300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000044c00000000000000000000000000000000000000000000000000000000000004b00000000000000000000000000000000000000000000000000000000000000514000000000000000000000000000000000000000000000000000000000000057800000000000000000000000000000000000000000000000000000000000030210000000000000000000000000000000000000000000000000000000000002ee000000000000000000000000000000000000000000000000000000000000004b00000000000000000000000000000000000000000000000000000000000000514000000000000000000000000000000000000000000000000000000000000057800000000000000000000000000000000000000000000000000000000000005dc00000000000000000000000000000000000000000000000000000000000003e87472616365310000000000000000000000000000000000000000000000000000"

		// Set other required fields
		log.blockNumber = "0xc8" // 200 in hex
		log.transactionHash = "0xaddaccountlimit3000"
		log.logIndex = "0x0"
		log.blockHash = "0xblockhash123"
		log.transactionIndex = "0x0"
		log.removed = false

		return log
	}

	/**
	 * Create a proper ForceBurn log for normal testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with ForceBurn event data
	 */
	static class ForceBurnDataStruct extends StaticStruct {
		ForceBurnDataStruct(
				Uint16 zoneId,
				Uint256 dischargeAmount
		) {
			super(Arrays.asList(zoneId, dischargeAmount))
		}
	}

	/**
	 * Create a proper AfterBalance log for normal testing
	 * This matches the exact structure expected by the ABI parser
	 * @return FromAfterBalanceDataStruct
	 */
	static class FromAfterBalanceDataStruct extends StaticStruct {
		FromAfterBalanceDataStruct(
				Uint16 zoneId,
				Uint256 balance
		) {
			super(Arrays.asList(zoneId, balance))
		}
	}

	/**
	 * Create a proper AfterBalance log for normal testing
	 * This matches the exact structure expected by the ABI parser
	 * @return ToAfterBalanceDataStruct
	 */
	static class ToAfterBalanceDataStruct extends StaticStruct {
		ToAfterBalanceDataStruct(
				Uint16 zoneId,
				Uint256 balance
		) {
			super(Arrays.asList(zoneId, balance))
		}
	}

	/**
	 * Create a mock ForceBurn event definition for testing
	 * Event: ForceBurn(bytes32 validatorId, bytes32 accountId, bytes32 traceId, uint256 burnedAmount,
	 * uint256 burnedBalance, tuple[] forceDischarge)
	 * Based on the actual Token.json ABI structure with proper tuple definition
	 * @return Event definition for Transfer
	 */
	static Event createMockForceBurnEvent() {
		// Use custom DynamicArray TypeReference that EventEncoder can handle
		TypeReference<DynamicArray<ForceBurnDataStruct>> tupleTypeReference = new TypeReference<DynamicArray<ForceBurnDataStruct>>(false) {
		}

		def parameters = [
				new TypeReference<Bytes32>(false) {}, // validatorId (non-indexed)
				new TypeReference<Bytes32>(false) {}, // accountId (non-indexed)
				new TypeReference<Bytes32>(false) {}, // traceId (non-indexed)
				new TypeReference<Uint256>(false) {}, // burnedAmount (non-indexed)
				new TypeReference<Uint256>(false) {}, // burnedBalance (non-indexed)
				tupleTypeReference, // forceDischarge (tuple[], non-indexed)
		]
		return new Event("ForceBurn", parameters)
	}

	/**
	 * Create a proper ForceBurn log for testing
	 * This matches the exact structure expected by the ABI parser
	 * @return Log object with ForceBurn event data
	 */
	static Log createMockForceBurnLog() {
		def log = new Log()

		// Use Account contract address
		log.address = "0x993366A606A99129e56B4b99B27e428ba1Cb672f"

		def forceBurnEvent = createMockForceBurnEvent()
		def eventSignature = EventEncoder.encode(forceBurnEvent)
		log.topics = [eventSignature]

		// Build the complete data field for ForceBurn
		// the log data below is encode from forceBurnEvent with value
		log.data = "0x38383838000000000000000000000000000000000000000000000000000000003330330000000000000000000000000000000000000000000000000000000000747261636549640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003e8000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000bb900000000000000000000000000000000000000000000000000000000000001f4"

		// Set other required fields
		log.blockNumber = "0xc8" // 200 in hex
		log.transactionHash = "0xabc123456789"
		log.logIndex = "0x0"
		log.blockHash = "0xblockhash123"
		log.transactionIndex = "0x0"
		log.removed = false

		return log
	}

	static Event createMockAddAccountEvent() {
		def parameters = [
				// validatorId (indexed)
				new TypeReference<Bytes32>(true) {},
				// accountId (non-indexed)
				new TypeReference<Bytes32>(false) {},
				// identified (non-indexed)
				new TypeReference<Bool>(false) {},
				// enabled (non-indexed)
				new TypeReference<Bool>(false) {},
				// limitAmounts (non-indexed)
				new TypeReference<DynamicArray<Uint256>>(false) {},
				// traceId (non-indexed)
				new TypeReference<Bytes32>(false) {},
		]
		return new Event("AddAccount", parameters)
	}

	/**
	 * Create a proper AfterBalance event for testing
	 * @return
	 */
	static Event createMockAfterBalanceEvent() {
		// Use custom DynamicArray TypeReference that EventEncoder can handle
		TypeReference<DynamicArray<FromAfterBalanceDataStruct>> fromAfterBalance = new TypeReference<DynamicArray<FromAfterBalanceDataStruct>>(false) {
		}

		TypeReference<DynamicArray<ToAfterBalanceDataStruct>> toAfterBalance = new TypeReference<DynamicArray<ToAfterBalanceDataStruct>>(false) {
		}

		def parameters = [
				fromAfterBalance, // fromAfterBalance (tuple[], non-indexed)
				toAfterBalance, // toAfterBalance (tuple[], non-indexed)
				// traceId (non-indexed)
				new TypeReference<Bytes32>(false) {},
		]
		return new Event("AfterBalance", parameters)
	}

	/**
	 * Create a proper AddAccount log for testing
	 * @return
	 */
	static Log createMockAddAccountLog() {
		def log = new Log()

		// Use Validator contract address
		log.address = "0xfdc1a198656D23B75d15c3d37194Ad5fABf17081"

		def event = createMockAddAccountEvent()
		def eventSignature = EventEncoder.encode(event)
		print("AddAccountLog eventSignature: $eventSignature")
		log.topics = [eventSignature,
					  "0x3838383800000000000000000000000000000000000000000000000000000000"]

		// Build the complete data field for AddAccount
		// the log data below is encode from AddAccount Event with value
		log.data = "0x33303300000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000a074726163653100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005000000000000000000000000000000000000000000000000000000000000c350000000000000000000000000000000000000000000000000000000000000c350000000000000000000000000000000000000000000000000000000000000c350000000000000000000000000000000000000000000000000000000000000c350000000000000000000000000000000000000000000000000000000000000c350"

		// Set other required fields
		log.logIndex = "0x0"
		log.blockHash = "0xblockhash123"
		log.transactionIndex = "0x0"
		log.removed = false

		return log
	}

	/**
	 * Create a proper AfterBalance log for testing
	 * @return Log
	 */
	static Log createMockAfterBalanceLog() {
		def log = new Log()

		// Use Account contract address
		log.address = "0x993366A606A99129e56B4b99B27e428ba1Cb672f"

		def event = createMockAfterBalanceEvent()
		def eventSignature = EventEncoder.encode(event)
		print("AfterBalance eventSignature: $eventSignature")
		log.topics = [eventSignature]

		// Build the complete data field for AddAccount
		// the log data below is encode from AddAccount Event with value
		log.data = "0x00000000000000000000000000000000000000000000000000000000000000600000000000000000000000000000000000000000000000000000000000000100747261636549640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000bb800000000000000000000000000000000000000000000000000000000000000640000000000000000000000000000000000000000000000000000000000000bb9000000000000000000000000000000000000000000000000000000000000006400000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000bb80000000000000000000000000000000000000000000000000000000000000000"

		// Set other required fields
		log.logIndex = "0x0"
		log.blockHash = "0xblockhash123"
		log.transactionIndex = "0x0"
		log.removed = false

		return log
	}


	/**
	 * Convert a 20-byte address to 32-byte hex string
	 *
	 * @param address20Bytes String
	 * @return the 32-byte hex string
	 */
	private static String to32Bytes(String address20Bytes) {
		if (address20Bytes == null) return null
		String hex = address20Bytes.toLowerCase().replace("0x", "")
		if (hex.length() != 40) throw new IllegalArgumentException("Not a valid 20-byte address")
		return "0x" + String.format("%064x", new BigInteger(hex, 16))
	}
}
